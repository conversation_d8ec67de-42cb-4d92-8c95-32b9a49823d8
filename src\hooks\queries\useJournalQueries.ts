/**
 * Journal Queries Hook
 * React Query hooks for journal entries with optimized caching and error handling
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
} from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import {
  JournalEntry,
  CreateJournalEntryPayload,
  UpdateJournalEntryPayload,
  FormattedJournalEntry,
  JournalStats,
  ApiResponse,
} from '@/types';
import {
  getJournalEntries,
  createJournalEntry,
  updateJournalEntry,
  deleteJournalEntry,
  getJournalStats,
  formatJournalEntryForDisplay,
} from '@/services/journalService';
import { queryKeys, cacheUtils } from '@/config/queryClient.config';
import { toast } from 'sonner';

/**
 * Hook to fetch all journal entries for the current user
 */
export const useJournalEntries = (
  options?: Omit<
    UseQueryOptions<JournalEntry[], Error, FormattedJournalEntry[]>,
    'queryKey' | 'queryFn'
  >
) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: queryKeys.journalEntries.list(user?.id || ''),
    queryFn: async (): Promise<FormattedJournalEntry[]> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await getJournalEntries(user.id);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to fetch journal entries');
      }

      return response.data.map(formatJournalEntryForDisplay);
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};



/**
 * Hook to fetch journal statistics
 */
export const useJournalStats = (
  options?: Omit<UseQueryOptions<JournalStats, Error>, 'queryKey' | 'queryFn'>
) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: queryKeys.journalEntries.stats(user?.id || ''),
    queryFn: async (): Promise<JournalStats> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await getJournalStats(user.id);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to fetch journal statistics');
      }

      return response.data;
    },
    enabled: !!user?.id,
    staleTime: 15 * 60 * 1000, // 15 minutes for stats
    cacheTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

/**
 * Hook to create a new journal entry
 */
export const useCreateJournalEntry = (
  options?: UseMutationOptions<JournalEntry, Error, CreateJournalEntryPayload>
) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: CreateJournalEntryPayload): Promise<JournalEntry> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await createJournalEntry(payload, user.id);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to create journal entry');
      }

      return response.data;
    },
    onSuccess: data => {
      console.log('🎉 React Query create mutation successful for entry:', data.id);

      // Invalidate all journal entry list queries (handles infinite queries with different filters)
      queryClient.invalidateQueries({
        queryKey: queryKeys.journalEntries.lists(),
      });

      // Cache the individual entry
      queryClient.setQueryData(
        queryKeys.journalEntries.detail(data.id),
        formatJournalEntryForDisplay(data)
      );

      // Invalidate stats to update counts
      queryClient.invalidateQueries({
        queryKey: queryKeys.journalEntries.stats(user?.id || ''),
      });

      console.log('✅ All caches invalidated successfully for entry creation');
      toast.success('Journal entry created successfully!');
    },
    onError: error => {
      toast.error(error.message || 'Failed to create journal entry');
    },
    ...options,
  });
};

/**
 * Hook to update a journal entry
 */
export const useUpdateJournalEntry = (
  options?: UseMutationOptions<
    JournalEntry,
    Error,
    { id: string; payload: UpdateJournalEntryPayload }
  >
) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      payload,
    }: {
      id: string;
      payload: UpdateJournalEntryPayload;
    }): Promise<JournalEntry> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await updateJournalEntry(id, user.id, payload);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to update journal entry');
      }

      return response.data;
    },
    onSuccess: (data, { id }) => {
      console.log('🎉 React Query update mutation successful for entry:', id);

      // Invalidate all journal entry list queries (handles infinite queries with different filters)
      queryClient.invalidateQueries({
        queryKey: queryKeys.journalEntries.lists(),
      });

      // Update the individual entry cache
      queryClient.setQueryData(
        queryKeys.journalEntries.detail(id),
        formatJournalEntryForDisplay(data)
      );

      // Invalidate stats to update counts if needed
      queryClient.invalidateQueries({
        queryKey: queryKeys.journalEntries.stats(user?.id || ''),
      });

      console.log('✅ All caches invalidated successfully for entry update');
      toast.success('Journal entry updated successfully!');
    },
    onError: error => {
      toast.error(error.message || 'Failed to update journal entry');
    },
    ...options,
  });
};

/**
 * Hook to delete a journal entry
 */
export const useDeleteJournalEntry = (options?: UseMutationOptions<void, Error, string>) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (entryId: string): Promise<void> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await deleteJournalEntry(entryId, user.id);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to delete journal entry');
      }
    },
    onSuccess: (_, entryId) => {
      console.log('🔄 React Query delete success - invalidating caches for entry:', entryId);

      // Invalidate all journal entry list queries (handles different filter combinations)
      queryClient.invalidateQueries({
        queryKey: queryKeys.journalEntries.lists(),
      });

      // Remove the individual entry from cache
      queryClient.removeQueries({
        queryKey: queryKeys.journalEntries.detail(entryId),
      });

      // Invalidate stats to update counts
      queryClient.invalidateQueries({
        queryKey: queryKeys.journalEntries.stats(user?.id || ''),
      });

      console.log('✅ All caches invalidated successfully');
      toast.success('Journal entry deleted successfully!');
    },
    onError: error => {
      toast.error(error.message || 'Failed to delete journal entry');
    },
    ...options,
  });
};

/**
 * Hook to prefetch journal entries (useful for performance optimization)
 */
export const usePrefetchJournalEntries = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const prefetchEntries = async () => {
    if (!user?.id) return;

    await queryClient.prefetchQuery({
      queryKey: queryKeys.journalEntries.list(user.id),
      queryFn: async () => {
        const response = await getJournalEntries(user.id);
        if (!response.success || !response.data) {
          throw new Error(response.error?.message || 'Failed to fetch journal entries');
        }
        return response.data.map(formatJournalEntryForDisplay);
      },
      staleTime: 5 * 60 * 1000,
    });
  };

  return { prefetchEntries };
};

/**
 * Hook to get cache utilities for journal entries
 */
export const useJournalCacheUtils = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => cacheUtils.invalidateJournalEntries(queryClient, user?.id || ''),
    invalidateEntry: (entryId: string) => cacheUtils.invalidateJournalEntry(queryClient, entryId),
    clearCache: () => {
      queryClient.removeQueries({
        queryKey: queryKeys.journalEntries.all,
      });
    },
  };
};
